import React from 'react';
import { observer } from 'mobx-react';
import ModalidadeLicitacaoIndexStore from '../../stores/modalidadeLicitacao/indexStore';
import IndexDataTable from 'fc/components/IndexDataTable';
import FcButton from 'fc/components/FcButton';
import { PrimeIcons } from 'primereact/api';
import AppBreadCrumb from 'fc/components/AppBreadCrumb';
import UrlRouter from '../../constants/UrlRouter';
import GenericIndexPage from 'fc/pages/GenericIndexPage';
import AdvancedSearch from 'fc/components/AdvancedSearch';
import PermissionProxy from 'fc/components/PermissionProxy';
import AccessPermission from '~/constants/AccessPermission';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import { getMultipleValuesByKey, getValueDate } from 'fc/utils/utils';
import { DATE_FORMAT, DATE_PARSE_FORMAT } from 'fc/utils/date';

@observer
class ModalidadeLicitacaoIndexPage extends GenericIndexPage {
  constructor(props) {
    super(props, AccessPermission.modalidadeLicitacao);
    this.store = new ModalidadeLicitacaoIndexStore();
    this.state = {
      idRemove: null,
    };
  }

  render() {
    const columns = [
      {
        field: 'nome',
        header: 'Nome',
        sortable: true,
        style: { width: '30%' },
      },
      {
        field: 'vigenciaDe',
        header: 'Vigência De',
        sortable: true,
        body: ({ vigenciaDe }) => getValueDate(vigenciaDe, DATE_FORMAT, DATE_PARSE_FORMAT),
        style: { width: '15%' },
      },
      {
        field: 'vigenciaAte',
        header: 'Vigência Até',
        sortable: true,
        body: ({ vigenciaAte }) => getValueDate(vigenciaAte, DATE_FORMAT, DATE_PARSE_FORMAT),
        style: { width: '15%' },
      },
      {
        field: 'permiteConsorcio',
        header: 'Permite Consórcio',
        sortable: true,
        body: ({ permiteConsorcio }) => (permiteConsorcio ? 'Sim' : 'Não'),
        style: { width: '15%' },
      },
      {
        field: 'legislacao',
        header: 'Legislação',
        sortable: true,
        body: ({ legislacao }) =>
          getMultipleValuesByKey(legislacao, DadosEstaticosService.getTipoLicitacaoLegislacao()),
        style: { width: '15%' },
      },
      {
        style: { width: '110px' },
        body: (rowData) => {
          return (
            <div className="actions p-d-flex p-jc-end">
              <PermissionProxy resourcePermissions={this.getWritePermission()}>
                <FcButton
                  icon="pi pi-pencil"
                  className="p-button-sm p-button-success p-mr-2"
                  onClick={() =>
                    this.pushUrlToHistory(UrlRouter.administracao.modalidadeLicitacao.editar.replace(':id', rowData.id))
                  }
                />
              </PermissionProxy>
              <PermissionProxy resourcePermissions={this.getWritePermission()}>
                <FcButton
                  icon="pi pi-trash"
                  className="p-button-sm p-button-danger"
                  onClick={() => {
                    this.setState({ idRemove: rowData.id });
                    this.store.toggleShowConfirmDialog();
                  }}
                />
              </PermissionProxy>
            </div>
          );
        },
      },
    ];

    const header = (
      <div className="table-header">
        <PermissionProxy resourcePermissions={this.getWritePermission()}>
          <FcButton
            className="p-button"
            label="Novo"
            style={{ marginBottom: '5px', marginRight: '5px' }}
            icon={PrimeIcons.PLUS}
            onClick={() => this.pushUrlToHistory(UrlRouter.administracao.modalidadeLicitacao.novo)}
          />
        </PermissionProxy>
        {this.renderTableDataExport(columns, 'modalidadesLicitacao')}
      </div>
    );

    const { listKey, loading } = this.store;
    const { getDefaultTableProps } = this;

    const breacrumbItems = [{ label: 'Modalidades de Licitação' }];

    return (
      <PermissionProxy resourcePermissions={this.getReadPermission()} blockOnFail>
        <AppBreadCrumb items={breacrumbItems} />
        <div className="card page index-table">
          <AdvancedSearch
            searchParams={this.store.getAdvancedSearchParams()}
            store={this.store}
            searchFields={['nome']}
          />
          <IndexDataTable
            setColumnsToExport={this.setColumnsToExport}
            columns={columns}
            value={listKey}
            header={header}
            loading={loading}
            {...getDefaultTableProps()}
          />
          {this.store.isConfirmDialogVisible && this.confirmRemove(this.state.idRemove)}
        </div>
      </PermissionProxy>
    );
  }
}

ModalidadeLicitacaoIndexPage.displayName = 'ModalidadeLicitacaoIndexPage';

export default ModalidadeLicitacaoIndexPage;
